package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strconv"
	"time"

	redis "github.com/redis/go-redis/v9"
	"gitlab.itingluo.com/backend/ivankabasepush/cache"
	"gitlab.itingluo.com/backend/ivankabasepush/models"
	std "gitlab.itingluo.com/backend/ivankastd"
)

func main() {
	fmt.Println("=== Redis连接诊断工具 ===")

	// 1. 加载配置
	config, err := loadConfig()
	if err != nil {
		fmt.Printf("❌ 配置加载失败: %v\n", err)
		return
	}

	fmt.Println("✅ 配置加载成功")
	fmt.Printf("Redis配置: %s:%s DB:%s\n", config.C.RedisHost, config.C.RedisPort, config.C.RedisDB)

	// 2. 测试Redis连接
	redisConfig := std.ConfigRedis{
		Host:        config.C.RedisHost,
		Port:        mustAtoi(config.C.RedisPort),
		Auth:        config.C.RedisAuth,
		IdleTimeout: mustAtoi(config.C.RedisIdleTimeout),
		DB:          mustAtoi(config.C.RedisDB),
	}

	fmt.Println("\n=== 测试Redis连接 ===")
	if !testRedisConnection(redisConfig) {
		return
	}

	// 3. 初始化Redis
	cache.InitRedis(redisConfig)
	models.Hospital = config

	// 4. 检查Redis中的数据
	fmt.Println("\n=== 检查Redis中的数据 ===")
	checkRedisData(config.C.TlClientId)

	// 5. 测试数据写入
	fmt.Println("\n=== 测试数据写入流程 ===")
	testDataWriteFlow(config)

	// 6. 检查开关状态
	fmt.Println("\n=== 检查配置开关 ===")
	checkSwitches(config)
}

func loadConfig() (*models.HospitalInfo, error) {
	config := &models.HospitalInfo{}
	configPath := "./conf/config.json"

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	fileContent, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	if err := json.Unmarshal(fileContent, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return config, nil
}

func testRedisConnection(config std.ConfigRedis) bool {
	fmt.Printf("尝试连接Redis: %s:%d DB:%d\n", config.Host, config.Port, config.DB)

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password: config.Auth,
		DB:       config.DB,
	})
	defer rdb.Close()

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	pong, err := rdb.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("❌ Redis连接失败: %v\n", err)
		fmt.Println("可能的原因:")
		fmt.Println("1. Redis服务器未启动")
		fmt.Println("2. 网络连接问题")
		fmt.Println("3. 认证信息错误")
		fmt.Println("4. 防火墙阻止连接")
		return false
	}

	fmt.Printf("✅ Redis连接成功: %s\n", pong)

	// 测试基本操作
	testKey := "test_connection_" + strconv.FormatInt(time.Now().Unix(), 10)
	err = rdb.Set(ctx, testKey, "test_value", time.Minute).Err()
	if err != nil {
		fmt.Printf("❌ Redis写入测试失败: %v\n", err)
		return false
	}

	val, err := rdb.Get(ctx, testKey).Result()
	if err != nil {
		fmt.Printf("❌ Redis读取测试失败: %v\n", err)
		return false
	}

	if val != "test_value" {
		fmt.Printf("❌ Redis读写不一致: 期望 'test_value', 实际 '%s'\n", val)
		return false
	}

	// 清理测试数据
	rdb.Del(ctx, testKey)

	fmt.Println("✅ Redis基本操作测试通过")
	return true
}

func checkRedisData(tlClientId int) {
	rs := cache.R()
	if rs == nil {
		fmt.Println("❌ Redis客户端未初始化")
		return
	}

	tlClientIdStr := strconv.Itoa(tlClientId)
	keys := []string{
		"InPatient-" + tlClientIdStr,
		"OutPatient-" + tlClientIdStr,
		"UpData-" + tlClientIdStr,
		"InLastTime-" + tlClientIdStr,
		"OutLastTime-" + tlClientIdStr,
	}

	ctx := context.Background()
	for _, key := range keys {
		exists, err := rs.Exists(ctx, key).Result()
		if err != nil {
			fmt.Printf("❌ 检查键 %s 失败: %v\n", key, err)
			continue
		}

		if exists == 0 {
			fmt.Printf("⚠️  键 %s 不存在\n", key)
			continue
		}

		// 检查数据类型和数量
		keyType, err := rs.Type(ctx, key).Result()
		if err != nil {
			fmt.Printf("❌ 获取键 %s 类型失败: %v\n", key, err)
			continue
		}

		switch keyType {
		case "hash":
			count, err := rs.HLen(ctx, key).Result()
			if err != nil {
				fmt.Printf("❌ 获取哈希 %s 长度失败: %v\n", key, err)
			} else {
				fmt.Printf("✅ 键 %s (hash): %d 条记录\n", key, count)
			}
		case "string":
			val, err := rs.Get(ctx, key).Result()
			if err != nil {
				fmt.Printf("❌ 获取字符串 %s 失败: %v\n", key, err)
			} else {
				fmt.Printf("✅ 键 %s (string): %s\n", key, val)
			}
		default:
			fmt.Printf("✅ 键 %s (%s): 存在\n", key, keyType)
		}
	}
}

func testDataWriteFlow(config *models.HospitalInfo) {
	fmt.Println("测试数据写入流程...")

	// 检查数据库连接
	dbName := config.C.DbName
	fmt.Printf("数据库类型: %s\n", dbName)

	fmt.Println("尝试执行一次数据同步...")

	// 这里可以调用实际的同步函数，但需要确保数据库已连接
	// dao.InPatientToRedis(tlClientId, "Up", dbName)

	fmt.Println("⚠️  需要数据库连接才能完整测试数据写入流程")
}

func checkSwitches(config *models.HospitalInfo) {
	fmt.Printf("TODB_SWITCH: %s\n", config.C.ToDbSwitch)

	if config.C.ToDbSwitch != "true" {
		fmt.Println("⚠️  TODB_SWITCH未启用，数据不会写入本地数据库")
	} else {
		fmt.Println("✅ TODB_SWITCH已启用")
	}
}

func mustAtoi(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		log.Fatalf("转换字符串 '%s' 为整数失败: %v", s, err)
	}
	return i
}
